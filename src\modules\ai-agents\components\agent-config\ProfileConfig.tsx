import { Chip, DatePicker, Icon, Input, Select, Button } from '@/shared/components/common';
import React, { KeyboardEvent, useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useAgentProfile, useUpdateAgentProfile } from '../../hooks/useAgentProfile';
import { ProfileData } from '../../types';
import { TypeAgentConfig } from '../../types/dto';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import { NotificationUtil } from '@/shared/utils/notification';

interface ProfileConfigProps {
  agentId?: string;
  initialData?: ProfileData;
  onSave?: (data: ProfileData) => void;
  mode?: 'create' | 'edit';
  typeAgentConfig?: TypeAgentConfig; // Type agent config để kiểm tra capabilities
}

/**
 * Component cấu hình thông tin profile của Agent
 */
const ProfileConfig: React.FC<ProfileConfigProps> = ({
  agentId,
  initialData,
  onSave,
  mode = 'create',
}) => {
  const { t } = useTranslation(['aiAgents', 'common']);

  // Fetch profile data từ API nếu có agentId và mode là edit
  const { data: profileResponse } = useAgentProfile(agentId && mode === 'edit' ? agentId : '');
  const updateProfileMutation = useUpdateAgentProfile();

  // Default values cho create mode
  const defaultProfileData: ProfileData = {
    birthDate: '01/08/2000',
    gender: 'Nữ',
    language: 'English',
    education: 'Cao đẳng',
    country: 'United States',
    position: 'Agent chatbot',
    skills: ['Tư vấn', 'Hỗ trợ khách hàng', 'dev'],
    personality: 'Chuyên nghiệp, hiệu quả, chính xác, khó',
  };

  const [profileData, setProfileData] = useState<ProfileData>(initialData || defaultProfileData);

  // State để track changes và initial data
  const [initialProfileData, setInitialProfileData] = useState<ProfileData>(profileData);
  const [hasChanges, setHasChanges] = useState(false);

  // Update local state khi có data từ API hoặc initialData
  useEffect(() => {
    if (mode === 'edit' && profileResponse) {
      const apiData = profileResponse;
      const newData = {
        birthDate: new Date(apiData.dateOfBirth).toLocaleDateString('vi-VN') || '01/01/2000',
        gender: apiData.gender === 'MALE' ? 'Nam' : apiData.gender === 'FEMALE' ? 'Nữ' : 'Khác',
        language: apiData.languages?.[0] || 'Tiếng Việt',
        education: apiData.education || 'Đại học',
        country: apiData.nations || 'Việt Nam',
        position: apiData.position || 'Sales Assistant',
        skills: apiData.skills || ['Tư vấn', 'Hỗ trợ khách hàng'],
        personality: apiData.personality?.join(', ') || 'Thân thiện, nhiệt tình, chuyên nghiệp',
      };
      setProfileData(newData);
      setInitialProfileData(newData); // Set initial data để so sánh changes
      setHasChanges(false); // Reset changes khi load data mới
    } else if (mode === 'create' && initialData) {
      // Create mode: sử dụng initialData từ props
      setProfileData(initialData);
      setInitialProfileData(initialData);
      setHasChanges(false);
    }
  }, [profileResponse, initialData, mode]);

  // Auto-save trong create mode khi profileData thay đổi
  // Sử dụng useCallback để memoize onSave function
  const memoizedOnSave = useCallback((data: ProfileData) => {
    if (onSave) {
      onSave(data);
    }
  }, [onSave]);

  useEffect(() => {
    if (mode === 'create') {
      memoizedOnSave(profileData);
    }
  }, [profileData, mode, memoizedOnSave]);

  // State cho input kỹ năng và tính cách
  const [skillInput, setSkillInput] = useState('');
  const [personalityInput, setPersonalityInput] = useState('');

  // Helper function để check changes
  const checkForChanges = (newData: ProfileData) => {
    const hasChanged = JSON.stringify(newData) !== JSON.stringify(initialProfileData);
    setHasChanges(hasChanged);
  };

  // Helper function để save data - chỉ gọi khi user click Save
  const handleSaveProfile = async () => {
    if (mode === 'create') {
      // Create mode: gọi callback onSave với dữ liệu hiện tại
      if (onSave) {
        onSave(profileData);
      }
      return;
    }

    if (!agentId || mode !== 'edit') {
      return;
    }

    // Validation
    if (!profileData.position.trim()) {
      NotificationUtil.error({
        message: t('aiAgents:profileConfig.positionPlaceholder'),
      });
      return;
    }

    try {
      // Convert ProfileData to API format
      const apiData = {
        gender: profileData.gender === 'Nam' ? 'MALE' as const :
                profileData.gender === 'Nữ' ? 'FEMALE' as const : 'OTHER' as const,
        dateOfBirth: new Date(profileData.birthDate).getTime(),
        position: profileData.position,
        education: profileData.education,
        skills: profileData.skills,
        personality: profileData.personality.split(',').map(p => p.trim()).filter(p => p),
        languages: [profileData.language],
        nations: profileData.country,
      };

      await updateProfileMutation.mutateAsync({
        agentId,
        data: apiData
      });

      // Update initial data và reset changes
      setInitialProfileData(profileData);
      setHasChanges(false);

      NotificationUtil.success({
        message: t('common:success'),
      });

      // Gọi callback onSave nếu có
      if (onSave) {
        onSave(profileData);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      NotificationUtil.error({
        message: t('common:error'),
      });
    }
  };

  // Xử lý khi thay đổi input text
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newData = {
      ...profileData,
      [name]: value
    };
    setProfileData(newData);
    checkForChanges(newData);
  };

  // Xử lý khi thay đổi select
  const handleSelectChange = (name: string, value: string | number | string[] | number[]) => {
    const newData = {
      ...profileData,
      [name]: value
    };
    setProfileData(newData);
    checkForChanges(newData);
  };

  // Helper function để parse date từ string format dd/MM/yyyy
  const parseDateString = (dateStr: string): Date => {
    try {
      // Nếu dateStr đã là ISO string hoặc timestamp
      if (dateStr.includes('-') || !isNaN(Number(dateStr))) {
        return new Date(dateStr);
      }

      // Parse format dd/MM/yyyy
      const parts = dateStr.split('/');
      if (parts.length === 3 && parts[0] && parts[1] && parts[2]) {
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
        const year = parseInt(parts[2], 10);
        return new Date(year, month, day);
      }

      // Fallback
      return new Date(dateStr);
    } catch (error) {
      console.error('Error parsing date:', dateStr, error);
      return new Date('2000-01-01');
    }
  };

  // Xử lý khi thay đổi ngày sinh
  const handleDateChange = (date: Date | null) => {
    if (date) {
      const formattedDate = date.toLocaleDateString('vi-VN');
      const newData = {
        ...profileData,
        birthDate: formattedDate
      };
      setProfileData(newData);
      checkForChanges(newData);
    }
  };

  // Xử lý khi thay đổi skills trực tiếp từ Select (không sử dụng trong thiết kế mới)

  // Xử lý khi thêm kỹ năng mới
  const handleAddSkill = () => {
    if (skillInput.trim() && !profileData.skills.includes(skillInput.trim())) {
      const newData = {
        ...profileData,
        skills: [...profileData.skills, skillInput.trim()]
      };
      setProfileData(newData);
      setSkillInput('');
      checkForChanges(newData);
    }
  };

  // Xử lý khi nhấn Enter trong input kỹ năng
  const handleSkillInputKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddSkill();
    }
  };

  // Xử lý khi xóa kỹ năng
  const handleRemoveSkill = (skill: string) => {
    const newData = {
      ...profileData,
      skills: profileData.skills.filter(s => s !== skill)
    };
    setProfileData(newData);
    checkForChanges(newData);
  };

  // Xử lý khi thêm tính cách mới
  const handleAddPersonality = () => {
    if (personalityInput.trim()) {
      const newPersonality = profileData.personality
        ? `${profileData.personality}, ${personalityInput.trim()}`
        : personalityInput.trim();

      const newData = {
        ...profileData,
        personality: newPersonality
      };
      setProfileData(newData);
      setPersonalityInput('');
      checkForChanges(newData);
    }
  };

  // Xử lý khi xóa tính cách
  const handleRemovePersonality = (traitToRemove: string) => {
    // Tách chuỗi tính cách thành mảng, lọc bỏ tính cách cần xóa, sau đó nối lại
    const traits = profileData.personality.split(',')
      .map(trait => trait.trim())
      .filter(trait => trait !== traitToRemove);

    const newPersonality = traits.join(', ');

    const newData = {
      ...profileData,
      personality: newPersonality
    };
    setProfileData(newData);
    checkForChanges(newData);
  };

  // Xử lý khi nhấn Enter trong input tính cách
  const handlePersonalityInputKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddPersonality();
    }
  };

  return (
    <ConfigComponentWrapper
      componentId="profile"
      title={
        <div className="flex items-center">
          <Icon name="user" size="md" className="mr-2" />
          <span>{t('aiAgents:profileConfig.title')}</span>
        </div>
      }
    >
      <div className="p-4 space-y-6">
        {/* Grid chính chia thành 12 cột */}
        <div className="grid grid-cols-12 gap-6">
          {/* Cột 8-12: Ngày sinh (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="birthDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('aiAgents:profileConfig.birthDate')}
            </label>
            <DatePicker
              value={parseDateString(profileData.birthDate)}
              onChange={handleDateChange}
              format="dd/MM/yyyy"
              placeholder={t('aiAgents:profileConfig.birthDate')}
              className="w-full"
            />
          </div>

          {/* Hàng 2 */
        /* Cột 1-2: Avatar (đã được xử lý bằng row-span-4) */}

          {/* Cột 3-7: Giới tính (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="gender" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('aiAgents:profileConfig.gender')}
            </label>
            <Select
              options={[
                { value: 'Nam', label: t('aiAgents:profileConfig.male') },
                { value: 'Nữ', label: t('aiAgents:profileConfig.female') },
                { value: 'Khác', label: t('aiAgents:profileConfig.other') }
              ]}
              value={profileData.gender}
              onChange={(value) => handleSelectChange('gender', value)}
              placeholder={t('aiAgents:profileConfig.gender')}
              className="w-full"
            />
          </div>

          {/* Cột 8-12: Trình độ học vấn (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="education" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('aiAgents:profileConfig.education')}
            </label>
            <Select
              options={[
                { value: 'Trung học', label: t('aiAgents:profileConfig.highSchool') },
                { value: 'Cao đẳng', label: t('aiAgents:profileConfig.college') },
                { value: 'Đại học', label: t('aiAgents:profileConfig.university') },
                { value: 'Sau đại học', label: t('aiAgents:profileConfig.postgraduate') }
              ]}
              value={profileData.education}
              onChange={(value) => handleSelectChange('education', value)}
              placeholder={t('aiAgents:profileConfig.education')}
              className="w-full"
            />
          </div>

          {/* Hàng 3 */
        /* Cột 1-2: Avatar (đã được xử lý bằng row-span-4) */}

          {/* Cột 3-7: Ngôn ngữ (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="language" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('aiAgents:profileConfig.language')}
            </label>
            <Select
              options={[
                { value: 'Tiếng Việt', label: 'Tiếng Việt' },
                { value: 'English', label: 'English' },
                { value: '中文', label: '中文' }
              ]}
              value={profileData.language}
              onChange={(value) => handleSelectChange('language', value)}
              placeholder={t('aiAgents:profileConfig.language')}
              className="w-full"
            />
          </div>

          {/* Cột 8-12: Quốc gia (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('aiAgents:profileConfig.country')}
            </label>
            <Select
              options={[
                { value: 'Việt Nam', label: 'Việt Nam' },
                { value: 'United States', label: 'United States' },
                { value: 'China', label: 'China' },
                { value: 'Japan', label: 'Japan' }
              ]}
              value={profileData.country}
              onChange={(value) => handleSelectChange('country', value)}
              placeholder={t('aiAgents:profileConfig.country')}
              className="w-full"
            />
          </div>

          {/* Hàng 4 */
        /* Cột 1-2: Avatar (đã được xử lý bằng row-span-4) */}

          {/* Cột 3-7: Chức vụ (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="position" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('aiAgents:profileConfig.position')}
            </label>
            <Input
              id="position"
              name="position"
              value={profileData.position}
              onChange={handleInputChange}
              placeholder={t('aiAgents:profileConfig.positionPlaceholder')}
              className="w-full"
            />
          </div>

          {/* Cột 8-12: Kỹ năng (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="skills" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('aiAgents:profileConfig.skills')}
            </label>
            <div className="flex flex-wrap gap-2 mb-2 min-h-[40px] dark:border-gray-700 rounded-md p-2">
              {profileData.skills.map((skill, index) => (
                <Chip
                  key={`skill-${index}`}
                  variant="default"
                  closable
                  onClose={() => handleRemoveSkill(skill)}
                >
                  {skill}
                </Chip>
              ))}
            </div>
            <div>
              <div className='w-full'>
                <Input
                  id="skillInput"
                  value={skillInput}
                  onChange={(e) => setSkillInput(e.target.value)}
                  onKeyDown={handleSkillInputKeyDown}
                  placeholder={t('aiAgents:profileConfig.skillPlaceholder')}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Hàng 5 (thêm hàng mới) */}
          {/* Cột 1-12: Tính cách (chiếm 12 cột) */}
          <div className="col-span-6">
            <label htmlFor="personality" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('aiAgents:profileConfig.personality')}
            </label>
            <div className="flex flex-wrap gap-2 mb-2 min-h-[40px] dark:border-gray-700 rounded-md p-2">
              {profileData.personality.split(',').map((trait, index) => (
                trait.trim() && (
                  <Chip
                    key={`trait-${index}`}
                    variant="default"
                    closable
                    onClose={() => handleRemovePersonality(trait.trim())}
                  >
                    {trait.trim()}
                  </Chip>
                )
              ))}
            </div>
            <div>
              <div className='w-full'>
                <Input
                  id="personalityInput"
                  value={personalityInput}
                  onChange={(e) => setPersonalityInput(e.target.value)}
                  onKeyDown={handlePersonalityInputKeyDown}
                  placeholder={t('aiAgents:profileConfig.personalityPlaceholder')}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>

        {mode === 'edit' && (
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="primary"
              size="md"
              onClick={handleSaveProfile}
              disabled={!hasChanges || updateProfileMutation.isPending}
              isLoading={updateProfileMutation.isPending}
              className="w-full"
            >
              <Icon name="save" size="sm" className="mr-2" />
              {t('common:save')}
            </Button>
          </div>
        )}
      </div>
    </ConfigComponentWrapper>
  );
};

export default ProfileConfig;
