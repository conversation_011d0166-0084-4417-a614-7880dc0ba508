import React, { useState } from 'react';
import {  FormItem, Input, Select, Icon, IconCard } from '@/shared/components/common';
import { ConvertField } from './ConvertFieldItem';
import { useTranslation } from 'react-i18next';

interface ConvertFieldFormProps {
  field?: ConvertField;
  onSave: (field: ConvertField) => void | Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

/**
 * Component form thêm/chỉnh sửa trường dữ liệu
 */
const ConvertFieldForm: React.FC<ConvertFieldFormProps> = ({
  field,
  onSave,
  onCancel,
  isLoading = false
}) => {
  const { t } = useTranslation('aiAgents');
  const [name, setName] = useState(field?.name || '');
  const [description, setDescription] = useState(field?.description || '');
  const [type, setType] = useState<ConvertField['type']>(field?.type || 'string');
  const [required, setRequired] = useState(field?.required || false);

  const isEditing = !!field;

  const handleSave = async () => {
    console.log('🔄 ConvertFieldForm handleSave called:', { name, description, type, required });

    if (!name || !description) {
      alert(t('convertConfig.pleaseEnterAllFields'));
      return;
    }

    const newField: ConvertField = {
      id: field?.id || `field-${Date.now()}`,
      name,
      description,
      enabled: field?.enabled ?? true,
      type,
      required
    };

    console.log('📤 ConvertFieldForm calling onSave with:', newField);
    await onSave(newField);
    console.log('✅ ConvertFieldForm onSave completed');
  };

  return (
    <div className="p-3 sm:p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3 sm:mb-4">
        {isEditing ? t('convertConfig.editField') : t('convertConfig.addField')}
      </h3>

      <div className="space-y-3 sm:space-y-4">
        <FormItem label={t('convertConfig.fieldName')}>
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder={t('convertConfig.fieldNamePlaceholder')}
            fullWidth
          />
        </FormItem>

        <FormItem label={t('convertConfig.fieldDescription')}>
          <Input
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder={t('convertConfig.fieldDescriptionPlaceholder')}
            fullWidth
          />
        </FormItem>

        <FormItem label={t('convertConfig.fieldType')}>
          <Select
            value={type}
            onChange={(value) => {
              if (typeof value === 'string') {
                setType(value as ConvertField['type']);
              }
            }}
            options={[
              { value: 'string', label: 'String' },
              { value: 'number', label: 'Number' },
              { value: 'boolean', label: 'Boolean' },
              { value: 'array_number', label: 'Array Number' },
              { value: 'array_string', label: 'Array String' },
              { value: 'enum', label: 'Enum' }
            ]}
            fullWidth
          />
        </FormItem>

        <div className="flex items-center">
          <div
            className={`w-5 h-5 rounded border ${required
              ? 'bg-primary border-primary'
              : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600'}
              flex items-center justify-center cursor-pointer`}
            onClick={() => setRequired(!required)}
          >
            {required && (
              <Icon name="check" size="sm" className="text-white" />
            )}
          </div>
          <label className="ml-2 text-sm text-gray-700 dark:text-gray-300 cursor-pointer" onClick={() => setRequired(!required)}>
            {t('convertConfig.fieldRequired')}
          </label>
        </div>
      </div>

      <div className="flex space-x-2 flex-col sm:flex-row sm:justify-end mt-4 gap-2 sm:gap-0">
        <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
            
          />
           <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={isEditing ? t('ommon.update') : t('common.add')}
            onClick={handleSave}
             disabled={!name || !description || isLoading}
            isLoading={isLoading}
          />
        
       
      </div>
    </div>
  );
};

export default ConvertFieldForm;
