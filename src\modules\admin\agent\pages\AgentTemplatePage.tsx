import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import AdminAgentTemplateGrid from '../components/AdminAgentTemplateGrid';

/**
 * Trang quản lý Agent Template
 */
const AgentTemplatePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleAddTemplate = () => {
    // TODO: Implement add template functionality
    console.log('Add template clicked');
  };

  const handleViewTrash = () => {
    navigate('/admin/agent/template/trash');
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddTemplate}
        items={[]}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('admin:agent.template.viewTrash', 'Xem thùng rác'),
            variant: 'default',
            onClick: handleViewTrash,
            className: 'text-gray-600 hover:text-gray-800',
          }
        ]}
      />

      <AdminAgentTemplateGrid
        searchTerm={search}
        page={page}
        limit={limit}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
        isTrashPage={false}
      />
    </div>
  );
};

export default AgentTemplatePage;
