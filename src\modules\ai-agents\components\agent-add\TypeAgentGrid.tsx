import React from 'react';
import { ResponsiveGrid } from '@/shared/components/common';
import TypeAgentCard, { TypeAgent } from './TypeAgentCard';
import { TypeAgentDto } from '../../api/type-agent.api';

export interface TypeAgentGridProps {
  /**
   * Mảng các type agent để hiển thị (có thể là TypeAgent cũ hoặc TypeAgentDto mới)
   */
  agents?: (TypeAgent | TypeAgentDto)[];

  /**
   * ID của agent đư<PERSON><PERSON> chọn
   */
  selectedAgentId?: number | null;

  /**
   * Có đang chọn custom agent không
   */
  isCustomAgentSelected?: boolean;

  /**
   * Hàm xử lý khi chọn agent
   */
  onSelectAgent?: (id: number) => void;

  /**
   * Hàm xử lý khi xem chi tiết agent
   */
  onViewAgent?: (id: number) => void;

  /**
   * Có hiển thị action buttons không
   */
  showActions?: boolean;
}

/**
 * Component hiển thị grid các Type Agent
 */
const TypeAgentGrid: React.FC<TypeAgentGridProps> = ({
  agents,
  selectedAgentId = null,
  onSelectAgent,
  onViewAgent,
  showActions = false
}) => {

  // Helper function để convert TypeAgentDto thành TypeAgent
  const convertToTypeAgent = (agent: TypeAgent | TypeAgentDto): TypeAgent => {
    // Nếu đã là TypeAgent thì return luôn
    if ('countTool' in agent) {
      return agent as TypeAgent;
    }

    // Convert TypeAgentDto thành TypeAgent
    const dto = agent as TypeAgentDto;
    return {
      id: dto.id,
      name: dto.name,
      description: dto.description || '',
      countTool: 0, // Default value
      config: {
        enableAgentProfileCustomization: dto.config.hasProfile,
        enableOutputToMessenger: false, // Default value
        enableOutputToWebsiteLiveChat: false, // Default value
        enableOutputToZaloOA: false, // Default value
        enableTaskConversionTracking: dto.config.hasConversion,
        enableResourceUsage: dto.config.hasResources,
        enableDynamicStrategyExecution: dto.config.hasStrategy,
        enableMultiAgentCollaboration: dto.config.hasMultiAgent
      }
    };
  };

  // Sử dụng dữ liệu được truyền vào và đảm bảo unique items
  const displayAgents = React.useMemo(() => {
    if (!agents || agents.length === 0) return [];

    // Loại bỏ duplicate items dựa trên ID và convert về TypeAgent
    const uniqueAgents = agents
      .filter((agent, index, self) =>
        index === self.findIndex(a => a.id === agent.id)
      )
      .map(convertToTypeAgent);

    return uniqueAgents;
  }, [agents]);

  // Xử lý khi chọn agent
  const handleSelectAgent = (id: number) => {
    if (onSelectAgent) {
      onSelectAgent(id);
    }
  };

  return (
    <ResponsiveGrid
     maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
      gap={{ xs: 4, md: 5, lg: 6 }}
      className="w-full"
    >
      {/* Các Type Agent Cards */}
      {displayAgents.map((agent) => (
        <TypeAgentCard
          key={agent.id}
          agent={agent}
          isSelected={selectedAgentId === agent.id}
          onClick={handleSelectAgent}
          onView={onViewAgent ?? (() => {})} 
          showActions={showActions}
        />
      ))}
    </ResponsiveGrid>
  );
};

export default TypeAgentGrid;
